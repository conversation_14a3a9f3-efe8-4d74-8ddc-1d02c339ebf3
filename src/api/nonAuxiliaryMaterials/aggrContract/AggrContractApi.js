import {deleteAction, getAction, postAction, putAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";

// 辅料购销合同信息列表
export const insertAggrContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.insert, params)
export const updateAggrContract = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.update}/${sid}`, params)
export const deleteAggrContract = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.delete}/${sids}`)
export const confirmAggrContract = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.confirm}/${sid}`)
export const sendAudit = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.sendAudit}/${sids}`)
export const invalidateAggrContract = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.invalidate}/${sids}`)
export const checkAggrContractNotCancel = (params) => window.majesty.httpUtil.postAction(ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.checkNotCancel, params)
export const copyVersion = (params) => window.majesty.httpUtil.postAction(ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.copyVersion, params)
export const insertAggrContractWithDetails = (params) => window.majesty.httpUtil.postAction(ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.insertAggrContractWithDetails, params)
export const updateAggrContractWithDetails = (params) => window.majesty.httpUtil.postAction(ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.updateAggrContractWithDetails, params)
export const validateDetailData = (params) => window.majesty.httpUtil.postAction(ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.validateDetailData, params)
export const handlerTransferNotice = (params) => window.majesty.httpUtil.postAction(ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.handlerTransferNotice, params)
export const saveTransferNotice = (params) => window.majesty.httpUtil.postAction(ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.saveTransferNotice, params)
export const checkTransferNotice = (params) => window.majesty.httpUtil.postAction(ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.checkTransferNotice, params)



// 辅料购销合同明细列表
export const insertAggrContractList = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.buyContractList.insert, params)
export const updateAggrContractList = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.auxiliaryMaterials.buyContractList.update}/${sid}`, params)
export const deleteAggrContractList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.auxiliaryMaterials.buyContractList.delete}/${sids}`)

/* 获取自定义配置信息 */
export const saveCustomWithDataId = (params) => window.majesty.httpUtil.postAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/saveCustomWithDataId', params)
export const getCustomVaueByTypeAndDataId = (params) => window.majesty.httpUtil.getAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/getCustomVaueByTypeAndDataId', params)
